<?php
/**
 * Simple cron trigger for testing
 * 
 * This script manually triggers the scheduled course activation
 * Access: /wp-content/plugins/learnpressium/trigger-cron.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Manual Cron Trigger</h1>";
echo "<p>Current time: " . current_time('mysql') . "</p>";

// Load required classes
require_once('includes/modules/enrollment/class-enrollment-database.php');
require_once('includes/modules/enrollment/class-simple-enrollment-manager.php');

echo "<h2>Triggering Scheduled Course Activation...</h2>";

$simple_manager = new Simple_Enrollment_Manager();
$activated_count = $simple_manager->activate_due_schedules();

echo "<p><strong>Result:</strong> {$activated_count} courses activated</p>";

if ($activated_count > 0) {
    echo "<p style='color: green; font-size: 18px;'>✅ SUCCESS! {$activated_count} scheduled courses were activated.</p>";
    echo "<p>Check your LearnPress profile - courses should now appear in 'In Progress' instead of 'Scheduled Course'.</p>";
} else {
    echo "<p style='color: orange; font-size: 18px;'>ℹ️ No courses were due for activation at this time.</p>";
    
    // Show what schedules exist
    global $wpdb;
    $scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';
    $pending_schedules = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$scheduled_table} WHERE status = 'pending' ORDER BY scheduled_start_date ASC"
    ));
    
    if (!empty($pending_schedules)) {
        echo "<h3>Pending Schedules:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Course ID</th><th>Scheduled Start</th><th>Status</th></tr>";
        foreach ($pending_schedules as $schedule) {
            echo "<tr>";
            echo "<td>{$schedule->user_id}</td>";
            echo "<td>{$schedule->course_id}</td>";
            echo "<td>{$schedule->scheduled_start_date}</td>";
            echo "<td>{$schedule->status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No pending schedules found in database.</p>";
    }
}

echo "<hr>";
echo "<p><a href='test-activation.php'>← Back to Full Test Page</a></p>";
?>
