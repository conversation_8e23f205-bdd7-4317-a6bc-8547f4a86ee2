<?php
/**
 * Enrollment Scheduler - Automatically activates scheduled courses
 * 
 * This class handles the automatic activation of scheduled courses when their date arrives.
 * It changes the status from 'scheduled' to 'enrolled' in LearnPress's user_items table,
 * which automatically makes them appear in the "In Progress" tab.
 */

if (!defined('ABSPATH')) {
    exit;
}

class Enrollment_Scheduler {
    
    private $simple_manager;
    
    public function __construct() {
        $this->simple_manager = new Simple_Enrollment_Manager();
    }
    
    /**
     * Initialize the scheduler
     */
    public function init() {
        // Add custom cron schedule
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));

        // Schedule the cron job
        add_action('wp', array($this, 'schedule_cron'));

        // Hook the activation function to the cron event
        add_action('learnpressium_activate_scheduled_courses', array($this, 'activate_scheduled_courses'));

        // Also check on admin pages for immediate activation
        add_action('admin_init', array($this, 'check_and_activate'));
    }

    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        $schedules['every_five_minutes'] = array(
            'interval' => 300, // 5 minutes in seconds
            'display'  => __('Every 5 Minutes', 'learnpressium')
        );
        $schedules['every_fifteen_minutes'] = array(
            'interval' => 900, // 15 minutes in seconds
            'display'  => __('Every 15 Minutes', 'learnpressium')
        );
        return $schedules;
    }
    
    /**
     * Schedule the cron job if not already scheduled
     */
    public function schedule_cron() {
        if (!wp_next_scheduled('learnpressium_activate_scheduled_courses')) {
            // Use custom 15-minute schedule for more frequent checks
            // This ensures scheduled courses are activated promptly
            wp_schedule_event(time(), 'every_fifteen_minutes', 'learnpressium_activate_scheduled_courses');
        }
    }
    
    /**
     * Activate scheduled courses that are due
     * This is the main function that moves courses from "Scheduled" to "In Progress"
     */
    public function activate_scheduled_courses() {
        $activated_count = $this->simple_manager->activate_due_schedules();
        
        if ($activated_count > 0) {
            error_log("Learnpressium: Activated {$activated_count} scheduled courses");
            
            // Optional: Send notification to admin
            $this->notify_admin_of_activations($activated_count);
        }
        
        return $activated_count;
    }
    
    /**
     * Check and activate immediately (for admin pages)
     */
    public function check_and_activate() {
        // Only run on specific admin pages to avoid performance issues
        $screen = get_current_screen();
        if ($screen && in_array($screen->id, array('learn-press_page_learn-press-tools', 'users', 'edit-lp_course'))) {
            $this->activate_scheduled_courses();
        }
    }
    
    /**
     * Notify admin of course activations
     */
    private function notify_admin_of_activations($count) {
        // Get admin email
        $admin_email = get_option('admin_email');
        
        // Prepare email
        $subject = sprintf('[%s] %d Scheduled Courses Activated', get_bloginfo('name'), $count);
        $message = sprintf(
            "Hello,\n\n%d scheduled courses have been automatically activated and are now available to students.\n\nYou can view the course enrollments in your LearnPress Tools section.\n\nBest regards,\nLearnpressium Plugin",
            $count
        );
        
        // Send email (optional - can be disabled)
        if (apply_filters('learnpressium_send_activation_notifications', false)) {
            wp_mail($admin_email, $subject, $message);
        }
    }
    
    /**
     * Manual activation trigger (for testing or manual runs)
     */
    public function manual_activate() {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        return $this->activate_scheduled_courses();
    }
    
    /**
     * Get next scheduled activation time
     */
    public function get_next_activation_time() {
        global $wpdb;
        
        $next_activation = $wpdb->get_var($wpdb->prepare(
            "SELECT MIN(meta_start.meta_value) 
             FROM {$wpdb->prefix}learnpress_user_items ui
             INNER JOIN {$wpdb->prefix}learnpress_user_itemmeta meta_start 
                 ON ui.user_item_id = meta_start.learnpress_user_item_id 
                 AND meta_start.meta_key = '_learnpressium_scheduled_start'
             WHERE ui.status = 'scheduled' 
             AND ui.item_type = 'lp_course'
             AND meta_start.meta_value > %s",
            current_time('mysql')
        ));
        
        return $next_activation;
    }
    
    /**
     * Get statistics about scheduled courses
     */
    public function get_scheduler_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Total scheduled courses
        $stats['total_scheduled'] = intval($wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->prefix}learnpress_user_items 
             WHERE status = 'scheduled' AND item_type = 'lp_course'"
        ));
        
        // Courses due for activation
        $stats['due_for_activation'] = intval($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) 
             FROM {$wpdb->prefix}learnpress_user_items ui
             INNER JOIN {$wpdb->prefix}learnpress_user_itemmeta meta_start 
                 ON ui.user_item_id = meta_start.learnpress_user_item_id 
                 AND meta_start.meta_key = '_learnpressium_scheduled_start'
             WHERE ui.status = 'scheduled' 
             AND ui.item_type = 'lp_course'
             AND meta_start.meta_value <= %s",
            current_time('mysql')
        )));
        
        // Next activation time
        $stats['next_activation'] = $this->get_next_activation_time();
        
        return $stats;
    }
    
    /**
     * Clean up cron job on deactivation
     */
    public function cleanup() {
        wp_clear_scheduled_hook('learnpressium_activate_scheduled_courses');
    }
}
