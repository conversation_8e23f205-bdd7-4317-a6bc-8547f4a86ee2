<?php
/**
 * Test script to debug scheduled course activation
 * 
 * This script helps debug why scheduled courses aren't being activated automatically.
 * Run this by accessing: /wp-content/plugins/learnpressium/test-activation.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Learnpressium Scheduled Course Activation Test</h1>";
echo "<p>Current time: " . current_time('mysql') . "</p>";
echo "<p>WordPress time: " . date('Y-m-d H:i:s') . "</p>";

// Load the enrollment module classes
require_once('includes/modules/enrollment/class-enrollment-database.php');
require_once('includes/modules/enrollment/class-simple-enrollment-manager.php');
require_once('includes/modules/enrollment/class-enrollment-scheduler.php');

echo "<h2>1. Database Connection Test</h2>";
$database = new Enrollment_Database();
echo "✓ Database class loaded<br>";

echo "<h2>2. Check Scheduled Courses</h2>";
global $wpdb;
$scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';

// Get all scheduled courses
$all_schedules = $wpdb->get_results("SELECT * FROM {$scheduled_table} ORDER BY scheduled_start_date DESC");
echo "<strong>Total schedules in database:</strong> " . count($all_schedules) . "<br>";

if (!empty($all_schedules)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>User ID</th><th>Course ID</th><th>Status</th><th>Scheduled Start</th><th>Current Time</th><th>Due?</th></tr>";
    
    $current_time = current_time('mysql');
    foreach ($all_schedules as $schedule) {
        $is_due = ($schedule->scheduled_start_date <= $current_time) ? 'YES' : 'NO';
        $due_class = $is_due === 'YES' ? 'style="background-color: #ffcccc;"' : '';
        
        echo "<tr {$due_class}>";
        echo "<td>{$schedule->schedule_id}</td>";
        echo "<td>{$schedule->user_id}</td>";
        echo "<td>{$schedule->course_id}</td>";
        echo "<td>{$schedule->status}</td>";
        echo "<td>{$schedule->scheduled_start_date}</td>";
        echo "<td>{$current_time}</td>";
        echo "<td><strong>{$is_due}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>3. Check Due Schedules</h2>";
$due_schedules = $database->get_schedules_to_activate();
echo "<strong>Schedules due for activation:</strong> " . count($due_schedules) . "<br>";

if (!empty($due_schedules)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Schedule ID</th><th>User ID</th><th>Course ID</th><th>Scheduled Start</th><th>Status</th></tr>";
    
    foreach ($due_schedules as $schedule) {
        echo "<tr>";
        echo "<td>{$schedule->schedule_id}</td>";
        echo "<td>{$schedule->user_id}</td>";
        echo "<td>{$schedule->course_id}</td>";
        echo "<td>{$schedule->scheduled_start_date}</td>";
        echo "<td>{$schedule->status}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>4. Check WordPress Cron Jobs</h2>";
$cron_jobs = wp_get_scheduled_event('learnpressium_activate_scheduled_courses');
if ($cron_jobs) {
    echo "✓ Cron job 'learnpressium_activate_scheduled_courses' is scheduled<br>";
    echo "Next run: " . date('Y-m-d H:i:s', $cron_jobs->timestamp) . "<br>";
} else {
    echo "❌ Cron job 'learnpressium_activate_scheduled_courses' is NOT scheduled<br>";
}

// Check for old conflicting cron job
$old_cron = wp_get_scheduled_event('learnpressium_process_scheduled_enrollments');
if ($old_cron) {
    echo "⚠️ Old conflicting cron job 'learnpressium_process_scheduled_enrollments' is still scheduled<br>";
    echo "Next run: " . date('Y-m-d H:i:s', $old_cron->timestamp) . "<br>";
}

echo "<h2>5. Manual Activation Test</h2>";
if (isset($_GET['activate']) && $_GET['activate'] === 'now') {
    echo "<strong>Running manual activation...</strong><br>";
    
    $simple_manager = new Simple_Enrollment_Manager();
    $activated_count = $simple_manager->activate_due_schedules();
    
    echo "✓ Manual activation completed<br>";
    echo "<strong>Courses activated:</strong> {$activated_count}<br>";
    
    if ($activated_count > 0) {
        echo "<p style='color: green;'>SUCCESS! {$activated_count} scheduled courses were activated.</p>";
        
        // Show updated schedules
        echo "<h3>Updated Schedules:</h3>";
        $updated_schedules = $wpdb->get_results("SELECT * FROM {$scheduled_table} WHERE status = 'activated' ORDER BY updated_at DESC LIMIT 10");
        if (!empty($updated_schedules)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>User ID</th><th>Course ID</th><th>Status</th><th>Updated At</th></tr>";
            foreach ($updated_schedules as $schedule) {
                echo "<tr>";
                echo "<td>{$schedule->schedule_id}</td>";
                echo "<td>{$schedule->user_id}</td>";
                echo "<td>{$schedule->course_id}</td>";
                echo "<td>{$schedule->status}</td>";
                echo "<td>{$schedule->updated_at}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check LearnPress enrollments
        echo "<h3>LearnPress Enrollments Created:</h3>";
        $lp_enrollments = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}learnpress_user_items 
             WHERE ref_type = 'learnpressium' 
             AND start_time >= %s 
             ORDER BY start_time DESC",
            date('Y-m-d H:i:s', strtotime('-1 hour'))
        ));
        
        if (!empty($lp_enrollments)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>User ID</th><th>Course ID</th><th>Status</th><th>Graduation</th><th>Start Time</th></tr>";
            foreach ($lp_enrollments as $enrollment) {
                echo "<tr>";
                echo "<td>{$enrollment->user_id}</td>";
                echo "<td>{$enrollment->item_id}</td>";
                echo "<td>{$enrollment->status}</td>";
                echo "<td>{$enrollment->graduation}</td>";
                echo "<td>{$enrollment->start_time}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>No recent LearnPress enrollments found.</p>";
        }
    } else {
        echo "<p style='color: orange;'>No courses were due for activation.</p>";
    }
} else {
    echo "<a href='?activate=now' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🚀 Run Manual Activation Now</a><br>";
}

echo "<h2>6. Cron System Test</h2>";
if (isset($_GET['test_cron']) && $_GET['test_cron'] === 'now') {
    echo "<strong>Testing cron system...</strong><br>";
    
    $scheduler = new Enrollment_Scheduler();
    $result = $scheduler->activate_scheduled_courses();
    
    echo "✓ Cron test completed<br>";
    echo "<strong>Result:</strong> {$result} courses activated<br>";
} else {
    echo "<a href='?test_cron=now' style='background: #46b450; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔄 Test Cron System</a><br>";
}

echo "<h2>7. Fix Cron Jobs</h2>";
if (isset($_GET['fix_cron']) && $_GET['fix_cron'] === 'now') {
    echo "<strong>Fixing cron jobs...</strong><br>";
    
    // Clear old conflicting cron job
    wp_clear_scheduled_hook('learnpressium_process_scheduled_enrollments');
    echo "✓ Cleared old cron job<br>";
    
    // Ensure correct cron job is scheduled
    if (!wp_next_scheduled('learnpressium_activate_scheduled_courses')) {
        wp_schedule_event(time(), 'hourly', 'learnpressium_activate_scheduled_courses');
        echo "✓ Scheduled correct cron job<br>";
    } else {
        echo "✓ Correct cron job already scheduled<br>";
    }
    
    echo "<p style='color: green;'>Cron jobs fixed!</p>";
} else {
    echo "<a href='?fix_cron=now' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔧 Fix Cron Jobs</a><br>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>First, click 'Fix Cron Jobs' to ensure the correct cron system is running</li>";
echo "<li>Then click 'Run Manual Activation Now' to immediately activate any due courses</li>";
echo "<li>Check your LearnPress profile to see if courses moved from 'Scheduled Course' to 'In Progress'</li>";
echo "</ol>";

echo "<p><em>This test script helps debug the automatic activation system. Delete this file after testing.</em></p>";
?>
